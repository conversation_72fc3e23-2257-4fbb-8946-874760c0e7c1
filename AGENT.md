# Clarisse Survival Kit Agent

## Development Commands

- **Lint**: `ruff check .` (extensive ruff configuration in pyproject.toml)
- **Format**: `ruff format .` (uses double quotes)

## Development Guidance

- **Types**: Extensive type hints with `ix.api` types, `Path`, `Any`, `Callable`
- **Quotes**: Double quotes (enforced by ruff)
- **Naming**: Snake_case for variables/functions, PascalCase for classes
- **Dependencies**: Heavy use of Clarisse IX API (`ix.api.*`), pathlib for paths

## Refactor Guidance

- **Type Hints**: Add type hints to all functions and variables
- **Documentation**: Add docstrings to all functions
- **Annotations**: Use `from __future__ import annotations` for modern type hints, to be able always use pipe operator `|` for type hints for python versions < 3.10
- **Error Handling**: Add try/except blocks to all functions
- **Code Duplication**: Refactor code duplication
- **Code Complexity**: Refactor code complexity
- **Code Readability**: Refactor code readability
- **Code Performance**: Refactor code performance
- **Code Security**: Refactor code security
- **Code Maintainability**: Refactor code maintainability
- **Code Efficiency**: Refactor code efficiency
- **Function Details**: Use guard clauses and early returns, avoid deep nesting, self documented, follow ruff recommendations
- **Variable Details**: Use descriptive variable names, avoid single letter names, avoid magic numbers
- **Import Details**: Always use from `import ...` instead of `import ...` whenever possible, avoid wildcard imports, use `as` for long module names or shadowing python builtins names
- **Directory_path**: Always use `Path` from `pathlib` for all paths if possible
- **Best Practices**: Follow PEP8, PEP257, PEP20, PEP257, PEP8, PEP20, PEP257, PEP8, PEP20, PEP257, PEP8, PEP20, PEP257, PEP8, PEP20, PEP257, PEP8, PEP20, PEP2
- **Main Entrance**: Use `if __name__ == "__main__":` for main entry point
- **To keep in mind**: This is a complex project with a lot of dependencies. Be careful when refactoring. Make sure not to break anything.
