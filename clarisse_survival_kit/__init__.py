from __future__ import annotations

from datetime import datetime
from logging import ERROR, basicConfig, debug
from os import environ, getenv
from pathlib import Path
from platform import system as platform_system
from site import getsitepackages, getusersitepackages
from sys import dont_write_bytecode, path, settrace

dont_write_bytecode = True

logging_filename = "clarisse_survival_kit.log"
settings_filename = "user_settings.py"


def get_isotropix_user_path() -> Path:
    if platform_system().lower() == "windows":
        return Path(getenv("APPDATA")) / "Isotropix\\"
    if platform_system().lower().startswith("linux"):
        return Path(Path.expanduser("~")) / ".isotropix/"
    if platform_system().lower() == "darwin":
        return Path.expanduser("~") / "Library/Preferences/Isotropix/"
    return Path("")


user_path = get_isotropix_user_path() / ".csk"

if not user_path:
    print("Could not generate log or user settings!!!")
else:
    path.append(user_path.as_posix())
    if not user_path.exists():
        user_path.mkdir(parents=True)

    init_path = user_path / "__init__.py"
    if not init_path.is_file():
        with open(init_path, "w+") as init_file:
            init_file.close()
        log_level = ERROR

    settings_path = user_path / settings_filename
    if not settings_path.is_file():
        with open(settings_path, "w+") as settings_file:
            settings_file.close()

    log_path = user_path / logging_filename
    if log_path.is_file():
        with open(log_path, "w") as log_file:
            log_file.truncate()

    sitepackages_folders = getsitepackages() + [getusersitepackages()]
    for sitepackages_folder in sitepackages_folders:
        if not Path(sitepackages_folder).is_dir():
            continue
        sub_folders = Path(sitepackages_folder).iterdir()
        for sub_folder in sub_folders:
            folder_path = Path(sitepackages_folder) / sub_folder
            if folder_path.is_dir() and sub_folder == "clarisse_survival_kit":
                print("Found CSK package location on disk")
                with open(settings_path, "a") as settings_file:
                    settings_file.write(f'\nPACKAGE_PATH = r"{folder_path}"')
                environ["CSK_PACKAGE_PATH"] = folder_path

    basicConfig(filename=log_path, level=log_level, format="%(message)s")
    log_start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    debug("--------------------------------------")
    debug("Log start: " + log_start)
