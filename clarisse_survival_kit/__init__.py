import datetime
import logging
import os
import platform
import site
import sys

sys.dont_write_bytecode = True

logging_filename = "clarisse_survival_kit.log"
settings_filename = "user_settings.py"


def get_isotropix_user_path() -> str:
    if platform.system().lower() == "windows":
        return os.path.join(os.getenv("APPDATA"), "Isotropix\\")
    if platform.system().lower().startswith("linux"):
        return os.path.join(os.path.expanduser("~"), ".isotropix/")
    if platform.system().lower() == "darwin":
        homedir = os.path.expanduser("~")
        return homedir + "/Library/Preferences/Isotropix/"
    return ""


user_path = os.path.join(get_isotropix_user_path(), ".csk")

if not user_path:
    print("Could not generate log or user settings!!!")
else:
    sys.path.append(os.path.normpath(user_path))
    if not os.path.exists(user_path):
        os.makedirs(user_path)

    init_path = os.path.join(user_path, "__init__.py")
    if not os.path.isfile(init_path):
        with open(init_path, "w+") as init_file:
            init_file.close()

    log_level = logging.ERROR

    settings_path = os.path.join(user_path, settings_filename)
    if not os.path.isfile(settings_path):
        with open(settings_path, "w+") as settings_file:
            settings_file.close()

    log_path = os.path.join(user_path, logging_filename)
    if os.path.isfile(log_path):
        with open(log_path, "w") as log_file:
            log_file.truncate()

    sitepackages_folders = site.getsitepackages() + [site.getusersitepackages()]
    for sitepackages_folder in sitepackages_folders:
        if not os.path.isdir(sitepackages_folder):
            continue
        sub_folders = os.listdir(sitepackages_folder)
        for sub_folder in sub_folders:
            folder_path = os.path.normpath(
                os.path.join(sitepackages_folder, sub_folder),
            )
            if os.path.isdir(folder_path) and sub_folder == "clarisse_survival_kit":
                print("Found CSK package location on disk")
                with open(settings_path, "a") as settings_file:
                    settings_file.write(f'\nPACKAGE_PATH = r"{folder_path}"')
                os.environ["CSK_PACKAGE_PATH"] = folder_path

    logging.basicConfig(filename=log_path, level=log_level, format="%(message)s")
    log_start = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logging.debug("--------------------------------------")
    logging.debug("Log start: " + log_start)
