#!/usr/bin/env python3
"""
Modern installation script for clarisse_survival_kit package.

This script demonstrates the modern way to install Python packages
and replaces the deprecated 'python setup.py install' method.
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}")
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stdout:
            print(f"✅ Success: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False


def main():
    """Main installation function."""
    print("🚀 Modern Python Package Installation for clarisse_survival_kit")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("setup.py").exists():
        print("❌ Error: setup.py not found. Please run this script from the package root directory.")
        sys.exit(1)
    
    print("\n📋 Available installation options:")
    print("1. Development install (editable) - recommended for development")
    print("2. Regular install - for production use")
    print("3. Build package for distribution")
    print("4. Install from built wheel")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        # Development installation
        success = run_command(
            [sys.executable, "-m", "pip", "install", "-e", "."],
            "Installing package in development mode (editable install)"
        )
        if success:
            print("\n✅ Development installation complete!")
            print("📝 You can now modify the code and changes will be reflected immediately.")
            print("🔧 The PostDevelopCommand has been executed.")
    
    elif choice == "2":
        # Regular installation
        success = run_command(
            [sys.executable, "-m", "pip", "install", "."],
            "Installing package in production mode"
        )
        if success:
            print("\n✅ Production installation complete!")
            print("🔧 The PostInstallCommand has been executed.")
            print("📁 Clarisse shelf integration should be set up automatically.")
    
    elif choice == "3":
        # Build package
        print("\n📦 Building package for distribution...")
        
        # Install build dependencies
        build_success = run_command(
            [sys.executable, "-m", "pip", "install", "build"],
            "Installing build dependencies"
        )
        
        if build_success:
            # Build the package
            success = run_command(
                [sys.executable, "-m", "build"],
                "Building wheel and source distribution"
            )
            
            if success:
                print("\n✅ Package built successfully!")
                print("📁 Check the 'dist/' directory for the built packages:")
                dist_dir = Path("dist")
                if dist_dir.exists():
                    for file in dist_dir.glob("*"):
                        print(f"   - {file.name}")
    
    elif choice == "4":
        # Install from wheel
        dist_dir = Path("dist")
        if not dist_dir.exists():
            print("❌ No dist/ directory found. Please build the package first (option 3).")
            return
        
        wheels = list(dist_dir.glob("*.whl"))
        if not wheels:
            print("❌ No wheel files found in dist/. Please build the package first (option 3).")
            return
        
        # Use the most recent wheel
        latest_wheel = max(wheels, key=lambda p: p.stat().st_mtime)
        
        success = run_command(
            [sys.executable, "-m", "pip", "install", str(latest_wheel)],
            f"Installing from wheel: {latest_wheel.name}"
        )
        
        if success:
            print("\n✅ Installation from wheel complete!")
    
    else:
        print("❌ Invalid choice. Please run the script again.")
        return
    
    print("\n" + "=" * 60)
    print("🎉 Installation process completed!")
    print("\n📚 Modern installation benefits:")
    print("   ✅ Proper dependency management")
    print("   ✅ Easy uninstallation with 'pip uninstall clarisse-survival-kit'")
    print("   ✅ Virtual environment support")
    print("   ✅ Metadata tracking")
    print("   ✅ Security and isolation")


if __name__ == "__main__":
    main()
