# Modern Installation Guide for clarisse_survival_kit

## 🚨 Important: Stop Using `python setup.py install`

The `python setup.py install` command is **deprecated** and should no longer be used. This guide shows you the modern, recommended approaches.

## Why `python setup.py install` is Deprecated

### Problems with the old method

- ❌ **No dependency resolution** - Can't handle version conflicts
- ❌ **No uninstall capability** - Can't remove packages cleanly
- ❌ **No isolation** - Directly modifies Python environment
- ❌ **Security risks** - Runs arbitrary code without sandboxing
- ❌ **No caching** - Rebuilds everything from scratch
- ❌ **Poor metadata** - Doesn't register properly with pip
- ❌ **Environment pollution** - Can break other packages

### Modern benefits

- ✅ **Proper dependency management** with pip
- ✅ **Easy uninstallation** with `pip uninstall`
- ✅ **Virtual environment support**
- ✅ **Metadata tracking** for better package management
- ✅ **Security and isolation**
- ✅ **Reproducible environments**

## 🎯 Recommended Modern Installation Methods

### 1. Development Installation (Recommended for Development)

```bash
# Install in editable mode - changes to code are immediately reflected
pip install -e .
```

**Benefits:**

- Code changes are immediately available without reinstalling
- Runs your custom `PostDevelopCommand`
- Perfect for development and testing

### 2. Production Installation

```bash
# Regular installation for production use
pip install .
```

**Benefits:**

- Installs the package normally
- Runs your custom `PostInstallCommand`
- Sets up Clarisse shelf integration automatically

### 3. Install from Git Repository

```bash
# Install directly from GitHub
pip install git+https://github.com/aydinyanik/clarisse_survival_kit.git

# Install specific branch or tag
pip install git+https://github.com/aydinyanik/clarisse_survival_kit.git@main
```

### 4. Build and Distribute

```bash
# Install build tools
pip install build

# Build the package
python -m build

# This creates:
# - dist/clarisse_survival_kit-2.0.0.tar.gz (source distribution)
# - dist/clarisse_survival_kit-2.0.0-py3-none-any.whl (wheel)

# Install from built wheel
pip install dist/clarisse_survival_kit-2.0.0-py3-none-any.whl
```

## 🔧 Package Management Commands

### Installation

```bash
# Development install (editable)
pip install -e .

# Production install
pip install .

# Install with verbose output
pip install -v .
```

### Uninstallation

```bash
# Uninstall the package (works with modern installs only!)
pip uninstall clarisse-survival-kit
```

### Upgrade

```bash
# Upgrade to latest version
pip install --upgrade .

# Force reinstall
pip install --force-reinstall .
```

### Check Installation

```bash
# List installed packages
pip list | grep clarisse

# Show package information
pip show clarisse-survival-kit
```

## 🏗️ Modern Package Structure

Your package now uses modern Python packaging standards:

### `pyproject.toml` (Primary configuration)

- Contains all package metadata
- Defines build system requirements
- Specifies dependencies and classifiers

### `setup.py` (Custom commands only)

- Minimal setup.py for custom installation commands
- Handles `PostDevelopCommand` and `PostInstallCommand`
- Maintains Clarisse shelf integration

## 🚀 Quick Start Script

Use the provided `install_modern.py` script for guided installation:

```bash
python3 install_modern.py
```

This script provides interactive options for:

1. Development installation
2. Production installation  
3. Building for distribution
4. Installing from built wheel

## 🔄 Migration from Old Method

### Before (Deprecated)

```bash
python setup.py install  # ❌ Don't use this anymore
```

### After (Modern)

```bash
pip install .  # ✅ Use this instead
```

## 🐍 Virtual Environment Best Practices

Always use virtual environments for development:

```bash
# Create virtual environment
python -m venv clarisse_env

# Activate (Linux/Mac)
source clarisse_env/bin/activate

# Activate (Windows)
clarisse_env\Scripts\activate

# Install in virtual environment
pip install -e .

# Deactivate when done
deactivate
```

## 🎯 Specific Commands for Your Package

### For Development

```bash
pip install -e .
```

This will:

- Install in editable mode
- Run `PostDevelopCommand`
- Allow immediate code changes

### For Production

```bash
pip install .
```

This will:

- Install normally
- Run `PostInstallCommand`
- Set up Clarisse shelf integration

### For Distribution

```bash
python -m build
pip install dist/clarisse_survival_kit-2.0.0-py3-none-any.whl
```

## 🔍 Troubleshooting

### If you get permission errors

```bash
# Install for current user only
pip install --user .
```

### If you need to force reinstall

```bash
pip install --force-reinstall .
```

### If dependencies conflict

```bash
# Install with dependency resolution
pip install --upgrade-strategy eager .
```

## 📚 Additional Resources

- [Python Packaging User Guide](https://packaging.python.org/)
- [pip documentation](https://pip.pypa.io/)
- [setuptools documentation](https://setuptools.pypa.io/)

---

**Remember:** Always use `pip install` instead of `python setup.py install` for modern Python package management!
