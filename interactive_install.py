#!/usr/bin/env python3
"""Modern installation script for clarisse_survival_kit package.

This script demonstrates the modern way to install Python packages
and replaces the deprecated 'python setup.py install' method.
"""

from __future__ import annotations

from pathlib import Path
from subprocess import CalledProcessError, run
from sys import executable
from sys import exit as sys_exit


def run_command(cmd: list[str], description: str) -> bool:
    """Run a command and handle errors.

    Args:
        cmd: List of command arguments to execute
        description: Human-readable description of the command

    Returns:
        True if command succeeded, False otherwise

    """
    print(f"\n🔄 {description}")
    print(f"Running: {' '.join(cmd)}")

    try:
        result = run(cmd, check=True, capture_output=True, text=True)  # noqa: S603
        if result.stdout:
            print(f"✅ Success: {result.stdout.strip()}")
    except CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False
    return True


def validate_package_directory() -> None:
    """Validate that we're in the correct package directory.

    Raises:
        SystemExit: If setup.py is not found in current directory

    """
    if not Path("setup.py").exists():
        print(
            "❌ Error: setup.py not found. Please run this script from the package root directory.",
        )
        sys_exit(1)


def _check_uv_available() -> bool:
    """Check if uv is available on the system.

    Returns:
        True if uv is available, False otherwise

    """
    try:
        run(  # noqa: S603
            [
                executable,
                "-c",
                "import subprocess; subprocess.run(['uv', '--version'], check=True, capture_output=True)",
            ],
            check=True,
            capture_output=True,
            text=True,
        )
    except (CalledProcessError, FileNotFoundError):
        return False
    else:
        return True


def display_menu() -> None:
    """Display the installation options menu."""
    print("🚀 Modern Python Package Installation for clarisse_survival_kit")
    print("=" * 60)

    uv_available = _check_uv_available()
    if uv_available:
        print("\n🚀 Package manager detected: pip and uv available")
        print("📋 Available installation options:")
        print("1. Development install (editable) - recommended for development")
        print("2. Regular install - for production use")
        print("3. Build package for distribution")
        print("4. Install from built wheel")
        print("5. Install with uv (faster alternative to pip)")
        print("6. Development install with uv (editable)")
    else:
        print("\n📋 Available installation options:")
        print("1. Development install (editable) - recommended for development")
        print("2. Regular install - for production use")
        print("3. Build package for distribution")
        print("4. Install from built wheel")


def get_user_choice() -> str:
    """Get and validate user choice from menu.

    Returns:
        User's choice as a string

    """
    uv_available = _check_uv_available()
    max_choice = "6" if uv_available else "4"
    return input(f"\nEnter your choice (1-{max_choice}): ").strip()


def print_completion_message() -> None:
    """Print the completion message with benefits."""
    print("\n" + "=" * 60)
    print("🎉 Installation process completed!")
    print("\n📚 Modern installation benefits:")
    print("   ✅ Proper dependency management")
    print("   ✅ Easy uninstallation with 'pip uninstall clarisse-survival-kit'")
    print("   ✅ Virtual environment support")
    print("   ✅ Metadata tracking")
    print("   ✅ Security and isolation")


def install_development() -> None:
    """Install package in development mode (editable install)."""
    success = run_command(
        [executable, "-m", "pip", "install", "-e", "."],
        "Installing package in development mode (editable install)",
    )
    if success:
        print("\n✅ Development installation complete!")
        print(
            "📝 You can now modify the code and changes will be reflected immediately.",
        )
        print("🔧 The PostDevelopCommand has been executed.")


def install_production() -> None:
    """Install package in production mode."""
    success = run_command(
        [executable, "-m", "pip", "install", "."],
        "Installing package in production mode",
    )
    if success:
        print("\n✅ Production installation complete!")
        print("🔧 The PostInstallCommand has been executed.")
        print("📁 Clarisse shelf integration should be set up automatically.")


def build_package() -> None:
    """Build package for distribution."""
    print("\n📦 Building package for distribution...")

    # Install build dependencies
    build_success = run_command(
        [executable, "-m", "pip", "install", "build"],
        "Installing build dependencies",
    )

    if not build_success:
        return

    # Build the package
    success = run_command(
        [executable, "-m", "build"],
        "Building wheel and source distribution",
    )

    if success:
        print("\n✅ Package built successfully!")
        print("📁 Check the 'dist/' directory for the built packages:")
        _list_built_packages()


def _list_built_packages() -> None:
    """List packages in the dist directory."""
    dist_dir = Path("dist")
    if dist_dir.exists():
        for file in dist_dir.glob("*"):
            print(f"   - {file.name}")


def _find_latest_wheel() -> Path:
    """Find the most recent wheel file in dist directory.

    Returns:
        Path to the latest wheel file

    Raises:
        SystemExit: If no dist directory or wheel files found

    """
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print(
            "❌ No dist/ directory found. Please build the package first (option 3).",
        )
        sys_exit(1)

    wheels = list(dist_dir.glob("*.whl"))
    if not wheels:
        print(
            "❌ No wheel files found in dist/. Please build the package first (option 3).",
        )
        sys_exit(1)

    # Use the most recent wheel
    return max(wheels, key=lambda p: p.stat().st_mtime)


def install_from_wheel() -> None:
    """Install package from built wheel file."""
    latest_wheel = _find_latest_wheel()

    success = run_command(
        [executable, "-m", "pip", "install", str(latest_wheel)],
        f"Installing from wheel: {latest_wheel.name}",
    )

    if success:
        print("\n✅ Installation from wheel complete!")


def install_with_uv() -> None:
    """Install package in production mode using uv."""
    success = run_command(
        ["uv", "pip", "install", "."],
        "Installing package in production mode with uv",
    )
    if success:
        print("\n✅ Production installation with uv complete!")
        print("🔧 The PostInstallCommand has been executed.")
        print("📁 Clarisse shelf integration should be set up automatically.")
        print("⚡ uv provides faster dependency resolution than pip!")


def install_development_with_uv() -> None:
    """Install package in development mode using uv (editable install)."""
    success = run_command(
        ["uv", "pip", "install", "-e", "."],
        "Installing package in development mode with uv (editable install)",
    )
    if success:
        print("\n✅ Development installation with uv complete!")
        print(
            "📝 You can now modify the code and changes will be reflected immediately.",
        )
        print("🔧 The PostDevelopCommand has been executed.")
        print("⚡ uv provides faster dependency resolution than pip!")


def process_user_choice(choice: str) -> None:
    """Process the user's installation choice.

    Args:
        choice: User's menu choice as string

    Raises:
        SystemExit: If invalid choice provided

    """
    installation_handlers = {
        "1": install_development,
        "2": install_production,
        "3": build_package,
        "4": install_from_wheel,
    }

    # Add uv options if uv is available
    if _check_uv_available():
        installation_handlers.update(
            {
                "5": install_with_uv,
                "6": install_development_with_uv,
            },
        )

    handler = installation_handlers.get(choice)
    if handler:
        handler()
    else:
        print("❌ Invalid choice. Please run the script again.")
        sys_exit(1)


def main() -> None:
    """Execute the main installation script workflow."""
    validate_package_directory()
    display_menu()
    choice = get_user_choice()
    process_user_choice(choice)
    print_completion_message()


if __name__ == "__main__":
    main()
